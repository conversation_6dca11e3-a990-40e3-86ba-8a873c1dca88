//@version=5
indicator("VCP 趨勢策略", overlay=true)

// === 核心參數 ===
vol_length = input.int(20, "成交量均線")
vol_mult = input.float(2.0, "高量倍數") 
vol_dryup = input.float(0.5, "枯竭倍數")
ma_fast = input.int(50, "快線")
ma_medium = input.int(150, "中線")
ma_slow = input.int(200, "慢線")
atr_len = input.int(14, "ATR週期")
vcp_bars = input.int(10, "VCP最少枯竭K線")
stop_mult = input.float(2.0, "停損倍數")
target_mult = input.float(4.0, "止盈倍數")
breakout_mult = input.float(3.0, "突破量倍數")
rs_period = input.int(100, "相對強度週期")

// === 核心指標 ===
ma50 = ta.sma(close, ma_fast)
ma150 = ta.sma(close, ma_medium) 
ma200 = ta.sma(close, ma_slow)
vol_ma = ta.sma(volume, vol_length)
atr = ta.atr(atr_len)
rs = (close - ta.lowest(low, rs_period)) / (ta.highest(high, rs_period) - ta.lowest(low, rs_period))

// === 趨勢判斷 ===
uptrend = close > ma50 and ma50 > ma150 and ma150 > ma200 and ma200 > ma200[20]
downtrend = close < ma50 and ma50 < ma150 and ma150 < ma200

// === 成交量分析 ===
high_vol = volume > vol_ma * vol_mult
dry_vol = volume < vol_ma * vol_dryup
close_pos = (close - low) / math.max(high - low, 0.00001)

// === VCP 形態檢測 ===
vcp_count = 0
for i = 0 to 20
    if dry_vol[i]
        vcp_count += 1
is_vcp = vcp_count >= vcp_bars

// === 交易信號 ===
// 口袋支點
is_up_day = close > open
vol_check = volume > ta.highest(volume * (close < open ? 1 : 0), 10)
pocket_pivot = is_up_day and vol_check

// 突破確認
breakout = pocket_pivot and high_vol and close_pos > 0.8 and high == ta.highest(high, 20)

// 買入條件
long_setup = uptrend and is_vcp and rs > 0.7
long_signal = long_setup and breakout

// 賣出條件  
short_setup = downtrend and high_vol and close_pos < 0.4
short_signal = short_setup and close < low[1]

// === 風險管理 ===
if long_signal
    stop_price = close - atr * stop_mult
    target_price = close + atr * target_mult
    alert("買入信號 | 入場:" + str.tostring(close) + " | 停損:" + str.tostring(stop_price) + " | 止盈:" + str.tostring(target_price))

if short_signal  
    stop_price = close + atr * stop_mult
    target_price = close - atr * target_mult
    alert("賣出信號 | 入場:" + str.tostring(close) + " | 停損:" + str.tostring(stop_price) + " | 止盈:" + str.tostring(target_price))

// === 繪圖 ===
plot(ma50, "MA50", color.orange)
plot(ma150, "MA150", color.blue) 
plot(ma200, "MA200", color.gray)

plotshape(long_signal, "買入", shape.labelup, location.belowbar, color.green, size=size.small)
plotshape(short_signal, "賣出", shape.labeldown, location.abovebar, color.red, size=size.small)
plotshape(pocket_pivot and not long_signal, "口袋支點", shape.circle, location.belowbar, color.yellow, size=size.tiny)

bgcolor(uptrend ? color.new(color.green, 95) : na, title="上升趨勢")
bgcolor(is_vcp ? color.new(color.yellow, 90) : na, title="VCP階段")
bgcolor(high_vol ? color.new(color.blue, 90) : na, title="高成交量")
bgcolor(dry_vol ? color.new(color.gray, 90) : na, title="成交量枯竭")

// === 策略說明 ===
var table info_table = table.new(position.bottom_right, 2, 4, border_width=1)

if barstate.islast
    table.cell(info_table, 0, 0, "市場階段", bgcolor=color.new(color.gray, 50), text_color=color.white)
    table.cell(info_table, 1, 0, uptrend ? "上升趨勢" : downtrend ? "下降趨勢" : "盤整", bgcolor=color.new(color.gray, 70), text_color=color.white)
    
    table.cell(info_table, 0, 1, "VCP狀態", bgcolor=color.new(color.gray, 50), text_color=color.white)  
    table.cell(info_table, 1, 1, is_vcp ? "成交量枯竭" : "正常", bgcolor=color.new(color.gray, 70), text_color=color.white)
    
    table.cell(info_table, 0, 2, "相對強度", bgcolor=color.new(color.gray, 50), text_color=color.white)
    table.cell(info_table, 1, 2, str.tostring(rs * 100, "#.0") + "%", bgcolor=color.new(color.gray, 70), text_color=color.white)
    
    table.cell(info_table, 0, 3, "當前信號", bgcolor=color.new(color.gray, 50), text_color=color.white)
    signal_text = long_signal ? "買入" : short_signal ? "賣出" : pocket_pivot ? "口袋支點" : "無信號"
    table.cell(info_table, 1, 3, signal_text, bgcolor=color.new(color.gray, 70), text_color=color.white)
